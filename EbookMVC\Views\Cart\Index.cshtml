@model EbookMVC.Models.Cart

@{
    ViewData["Title"] = "Giỏ hàng";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Giỏ hàng</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                    <li class="breadcrumb-item active">Giỏ hàng</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Thông báo -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> @TempData["ErrorMessage"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shopping-cart"></i>
                            Danh sách khóa học trong giỏ hàng
                        </h3>
                        <div class="card-tools">
                            @if (Model?.CartItems?.Any() == true)
                            {
                                <form asp-action="ClearCart" method="post" style="display: inline;" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa tất cả khóa học khỏi giỏ hàng?')">
                                    <button type="submit" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> Xóa tất cả
                                    </button>
                                </form>
                            }
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        @if (Model?.CartItems?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th style="width: 80px">Hình ảnh</th>
                                            <th>Tên khóa học</th>
                                            <th>Danh mục</th>
                                            <th style="width: 120px">Đơn giá</th>
                                            <th style="width: 100px">Số lượng</th>
                                            <th style="width: 120px">Thành tiền</th>
                                            <th style="width: 100px">Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.CartItems)
                                        {
                                            <tr>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                                    {
                                                        <img src="@item.Product.ImageUrl" alt="@item.Product.Name" 
                                                             class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                                             style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    }
                                                </td>
                                                <td>
                                                    <strong>@item.Product.Name</strong>
                                                    @if (!string.IsNullOrEmpty(item.Product.Description))
                                                    {
                                                        <br><small class="text-muted">@item.Product.Description.Substring(0, Math.Min(100, item.Product.Description.Length))...</small>
                                                    }
                                                </td>
                                                <td>@item.Product.Category?.Name</td>
                                                <td class="text-right">
                                                    <strong>@item.UnitPrice.ToString("N0") VNĐ</strong>
                                                </td>
                                                <td>
                                                    <form asp-action="UpdateQuantity" method="post" class="d-flex align-items-center">
                                                        <input type="hidden" name="cartItemId" value="@item.Id" />
                                                        <input type="number" name="quantity" value="@item.Quantity" 
                                                               min="1" max="10" class="form-control form-control-sm" 
                                                               style="width: 70px;" onchange="this.form.submit()">
                                                    </form>
                                                </td>
                                                <td class="text-right">
                                                    <strong class="text-success">@item.TotalPrice.ToString("N0") VNĐ</strong>
                                                </td>
                                                <td class="text-center">
                                                    <form asp-action="RemoveFromCart" method="post" style="display: inline;" 
                                                          onsubmit="return confirm('Bạn có chắc chắn muốn xóa khóa học này khỏi giỏ hàng?')">
                                                        <input type="hidden" name="cartItemId" value="@item.Id" />
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot>
                                        <tr class="bg-light">
                                            <td colspan="5" class="text-right"><strong>Tổng cộng:</strong></td>
                                            <td class="text-right">
                                                <strong class="text-success h5">@Model.TotalAmount.ToString("N0") VNĐ</strong>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Action buttons -->
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <a href="/Shop" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Tiếp tục mua hàng
                                    </a>
                                </div>
                                <div class="col-md-6 text-right">
                                    <a asp-controller="Order" asp-action="Checkout" class="btn btn-success btn-lg">
                                        <i class="fas fa-credit-card"></i> Thanh toán (@Model.TotalAmount.ToString("N0") VNĐ)
                                    </a>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
                                <h4 class="text-muted">Giỏ hàng của bạn đang trống</h4>
                                <p class="text-muted">Hãy thêm một số khóa học vào giỏ hàng để bắt đầu học tập!</p>
                                <a href="/Shop" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search"></i> Khám phá khóa học
                                </a>
                            </div>
                        }
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Auto-submit form when quantity changes
        $(document).ready(function() {
            $('input[name="quantity"]').on('change', function() {
                var form = $(this).closest('form');
                var quantity = $(this).val();
                
                if (quantity < 1) {
                    if (confirm('Bạn có muốn xóa sản phẩm này khỏi giỏ hàng?')) {
                        form.submit();
                    } else {
                        $(this).val(1);
                    }
                } else {
                    form.submit();
                }
            });
        });
    </script>
}
